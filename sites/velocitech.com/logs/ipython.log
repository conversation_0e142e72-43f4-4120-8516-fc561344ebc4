2025-08-01 08:59:55,871 INFO ipython === bench console session ===
2025-08-01 08:59:55,871 INFO ipython # Test the function manually
2025-08-01 08:59:55,871 INFO ipython import frappe
2025-08-01 08:59:55,871 INFO ipython from velocetec.api.work_order import _populate_notes_from_velocetec_costing
2025-08-01 08:59:55,871 INFO ipython # Create a test work order object
2025-08-01 08:59:55,871 INFO ipython test_wo = frappe._dict({
    "name": "TEST-WO",
        "production_item": "_Test Item",
            "sales_order": None,
                "custom_internal_notes": None,
                    "custom_external_notes": None
                    })
2025-08-01 08:59:55,871 INFO ipython print("Testing work order notes population...")
2025-08-01 08:59:55,871 INFO ipython print(f"Before: internal_notes = {test_wo.custom_internal_notes}, external_notes = {test_wo.custom_external_notes}")
2025-08-01 08:59:55,872 INFO ipython # Test the function
2025-08-01 08:59:55,872 INFO ipython _populate_notes_from_velocetec_costing(test_wo)
2025-08-01 08:59:55,872 INFO ipython print(f"After: internal_notes = {test_wo.custom_internal_notes}, external_notes = {test_wo.custom_external_notes}")
2025-08-01 08:59:55,872 INFO ipython print("Test completed successfully!")
2025-08-01 08:59:55,872 INFO ipython === session end ===
